package com.carsaver.configuration.uibuilder.service

import com.carsaver.configuration.api.ServiceException
import com.carsaver.configuration.shared.client.harbor.HarborClient
import com.carsaver.configuration.uibuilder.dynamo.LenderDeskRepository
import com.carsaver.configuration.uibuilder.model.Builder
import com.carsaver.configuration.uibuilder.model.ComponentType
import com.carsaver.configuration.uibuilder.model.Item
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

const val GET_LENDERS = "getLenders()"
const val GET_LINK_DESTINATIONS = "getLinkDestinations()"
const val GET_CTA_LINK_DESTINATIONS = "getCtaLinkDestinations()"
const val GET_PROGRAMS = "getPrograms()"
const val GET_SMART_LINK_QR_CODE_PROGRAMS = "getSmartLinkQrCodePrograms()"
const val GET_SMART_LINK_QR_CODE_DESTINATIONS = "getSmartLinkQrCodeLinkDestinations()"


@Service
class DropdownService(
    private val lenderDeskRepository: LenderDeskRepository,
    private val harborClient: HarborClient,
) {
    private val logger = LoggerFactory.getLogger(DropdownService::class.java)

    private val actions: Map<String, () -> List<Option>> = mapOf(
        GET_LENDERS to ::getLenders,
        GET_LINK_DESTINATIONS to ::getLinkDestinations,
        GET_CTA_LINK_DESTINATIONS to ::getCtaLinkDestinations,
        GET_PROGRAMS to ::getPrograms,
        GET_SMART_LINK_QR_CODE_PROGRAMS to { getSmartLinkQrCodePrograms(null) },
        GET_SMART_LINK_QR_CODE_DESTINATIONS to ::getSmartLinkQrCodeLinkDestinations,
    )

    fun fetchAndSetDynamicDropDownValues(builder: Builder, dealerId: String? = null) {
        val valuesCached: MutableMap<String, List<Option>> = mutableMapOf()
        val componentsHighLevelDropDown: Sequence<Item> = builder.sections
            .asSequence()
            .flatMap { it.components }
            .filter { it.inputs != null }
            .flatMap { it.inputs!! }
            .filter { it.type == ComponentType.DROPDOWN }
            .filter { it.function?.isNotEmpty() ?: false }

        val componentsGroupDropdown: Sequence<Item> = builder.sections
            .asSequence()
            .flatMap { it.components }
            .filter { it.inputs != null }
            .flatMap { it.inputs!! }
            .filter { it.type == ComponentType.ENABLED_GROUP }
            .filter { it.group != null }
            .map { it.group }
            .filter { it is List<*> }
            .flatMap { it as List<*> }
            .map { it as Item }
            .filter { it.type == ComponentType.DROPDOWN }
            .filter { it.function?.isNotEmpty() ?: false }


        setFunctionData(componentsHighLevelDropDown, valuesCached, dealerId)
        setFunctionData(componentsGroupDropdown, valuesCached, dealerId)
    }

    private fun setFunctionData(
        componentsHighLevelDropDown: Sequence<Item>,
        valuesCached: MutableMap<String, List<Option>>,
        dealerId: String? = null
    ) {
        componentsHighLevelDropDown.forEach {
            try {
                var values = valuesCached[it.function]

                if (values == null) {
                    logger.debug("Getting values for function: {} with dealerId: {}", it.function, dealerId ?: "null")
                    values = when(it.function) {
                        GET_SMART_LINK_QR_CODE_PROGRAMS -> getSmartLinkQrCodePrograms(dealerId)
                        else -> actions[it.function]?.invoke()
                    }

                    if (values != null) {
                        logger.debug("Retrieved {} values for function: {}", values.size, it.function)
                    }
                }

                if (values == null) throw ServiceException.serverError("Can't find function for ${it.function}")

                valuesCached[it.function!!] = values
                it.allowedValues = values
            } catch (e: Exception) {
                logger.error("Error processing function: {}", it.function, e)
                throw ServiceException.serverError("Error processing function: ${it.function}: ${e.message}")
            }
        }
    }

    internal fun getLenders(): List<Option> {
        val lenders = lenderDeskRepository.findAll().takeIf { it?.isNotEmpty() ?: false }
            ?: throw ServiceException.serverError("Problems retrieving Lenders from DynamoDB. (empty)")

        return lenders
            .filter { !it.name.isNullOrEmpty() }
            .map{ Option(value = it.financierId.toString(), text = it.name, label = it.name) }.toList()
    }

    private fun getLinkDestinations(): List<Option> {
        //TODO: Hydrate with real data
        return listOf(
            Option("Payment","Payment Options"),
            Option("RebatesIncentives", "Rebates & Incentives"),
            Option("TradeIn", "Trade-In"),
            Option("Financing", "Financing"),
            Option("Appointments", "Appointments")
        )
    }

    private fun getCtaLinkDestinations(): List<Option> {
        return listOf(
            Option("DEAL_PAGE","Deal Page"),
            Option("VDP", "Vehicle Details Page"),
            Option("PRE_QUALIFY", "Get Prequalified"),
            Option("TRADE_VALUE", "Get Trade Value"),
            Option("TRADE_VALUE_STAND_ALONE", "Standalone Trade"),
            Option("SELL_AT_HOME", "Sell@Home"),
            Option("FINANCE", "Finance Application"),
            Option("APPOINTMENT", "Schedule Appointment"),
            Option("CONTACT_DEALER", "Check Availability (Contact Dealer)"),
            Option("RETENTION", "Upgrade Landing Page")
        )
    }

    internal fun getPrograms(): List<Option> {
        val allPrograms = harborClient.getAllPrograms()
        allPrograms.let {
            return it.map { program ->
                Option(value = program["id"].toString(), label = program["name"].toString())
            }
        }
    }

    internal fun getSmartLinkQrCodePrograms(dealerId: String? = null): List<Option> {
        if (dealerId == null) {
            logger.warn("No dealer ID provided for getSmartLinkQrCodePrograms. Returning empty list.")
            return emptyList()
        }

        val allPrograms = harborClient.getAllPrograms()
        val dealerProgramSubscriptions = harborClient.getDealerProgramSubscriptions(dealerId)

        // Extract active program IDs from subscriptions
        val activeProgramIds = dealerProgramSubscriptions
            .filter {
                val status = it["status"]?.toString() ?: ""
                status.equals("LIVE", ignoreCase = true)
            }
            .mapNotNull {
                try {
                    it["programId"] as? String
                } catch (e: Exception) {
                    logger.warn("Failed to extract programId from subscription: {}", it)
                    null
                }
            }
            .toSet()

        // Filter all programs to only include those with an active subscription and product ID 102
        val filteredPrograms = allPrograms.filter { program ->
            val programIdMatches = program["id"] in activeProgramIds

            val product = program["product"] as? Map<*, *>
            val productId = product?.get("id")

            programIdMatches && productId == 102
        }

        // Build and return the list of Options
        val options = filteredPrograms.map { program ->
            Option(value = program["id"].toString(), label = program["name"].toString())
        }.sortedBy { it.label }

        return options
    }

    private fun getSmartLinkQrCodeLinkDestinations(): List<Option> {
        return listOf(
            Option("Home","Home Page"),
        )
    }
}

data class Option(
    val value: String?,
    @Deprecated(message = "use label instead") val text: String? = null,
    val label: String? = null,
    val description: String? = null,
    val disabled: Boolean = false
)
